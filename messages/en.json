{"template": "flux-ai-template", "theme": "dark", "header": {"logo": "FluxAI Studio", "nav": {"benefit": "Benefits", "stats": "Stats", "pricing": "Pricing", "testimonial": "Reviews", "faq": "FAQ"}, "cta": {"login": "Sign In", "signup": "Start Creating"}, "userMenu": {"myOrders": "My Orders", "signOut": "Sign Out", "profile": "My Profile"}}, "hero": {"title": "Launch Your SaaS in Record Time", "subtitle": "Ship SaaS Demo – The all-in-one SaaS template to build, launch, and grow your SaaS business fast", "description": "Ready-to-use SaaS template with built-in subscription payments, multi-language, SEO, user management, and more. Deploy in one click, enjoy a seamless developer experience.", "cta": {"primary": "Try the Demo Now", "secondary": "See Key Features"}}, "benefit": {"title": "Why Choose Ship SaaS Demo?", "subtitle": "All-in-one SaaS template for efficient startups", "benefits": [{"title": "Lightning-fast Launch", "description": "No complex setup. Deploy and go live in as little as 1 day with our template.", "icon": "speed"}, {"title": "Subscription & Payment Ready", "description": "Built-in Stripe subscription payments, multi-currency support, and invoicing for easy monetization.", "icon": "credit-card"}, {"title": "i18n & SEO Optimized", "description": "Multi-language support and best-in-class SEO practices for global reach.", "icon": "globe"}, {"title": "Robust User Management", "description": "OAuth login, role management, and user center included. Secure and reliable.", "icon": "user"}]}, "stats": {"title": "Trusted by Developers & Founders", "subtitle": "Hundreds of SaaS startups launched successfully", "stats": [{"value": "100+", "label": "SaaS Projects Launched", "description": "Number of SaaS products shipped with our template"}, {"value": "1 Day", "label": "Fastest Launch Time", "description": "From clone to production in as little as 1 day"}, {"value": "99.99%", "label": "System Uptime", "description": "Highly available architecture for business continuity"}, {"value": "24/7", "label": "Tech Support", "description": "Professional team, always on call"}]}, "pricing": {"title": "Transparent & Flexible Pricing", "subtitle": "Choose the best plan for your SaaS journey", "perMonth": "/month", "contactUs": "Contact Us", "getStarted": "Get Started", "buyNow": "Buy Now", "pleaseLogin": "Please login before making a purchase", "frequencies": {"monthly": "Monthly", "yearly": "Yearly"}, "plans": [{"name": "Basic", "monthlyPrice": 99, "yearlyPrice": 990, "originalPrice": 199, "description": "For solo developers and small teams to quickly validate SaaS ideas.", "features": ["Next.js template code", "SEO-optimized structure", "Blog & CMS", "Stripe subscription payments", "Supabase data storage", "Google OAuth login", "Multi-language support"]}, {"name": "Standard", "monthlyPrice": 199, "yearlyPrice": 1990, "originalPrice": 299, "description": "For growing teams, with more customization and scalability.", "features": ["All features in Basic", "Deploy with Vercel/Cloudflare", "Privacy policy generation", "Google Analytics integration", "Discord community support", "First-ship technical support", "Lifetime free updates"]}, {"name": "Premium", "monthlyPrice": 299, "yearlyPrice": 2990, "originalPrice": 399, "description": "For enterprise SaaS, with advanced features and priority service.", "features": ["All features in Standard", "More components to choose from", "AI business features & SDK", "User console", "Admin dashboard", "Credits management", "API keys management", "Priority technical support"]}]}, "testimonial": {"title": "The Choice of Developers Worldwide", "subtitle": "See what people say about Ship SaaS Demo", "testimonials": [{"content": "Ship SaaS Demo took our SaaS from idea to launch in just two days. Huge boost in team efficiency!", "author": {"name": "<PERSON>", "title": "CTO, Startup Team", "company": "Nebula Tech", "image": "/testimonials/1.jpg"}}, {"content": "The template integrates payments, i18n, and SEO out of the box. Saved us tons of repetitive work. Highly recommended for SaaS founders.", "author": {"name": "<PERSON>", "title": "Indie Developer", "company": "Freelance", "image": "/testimonials/2.jpg"}}, {"content": "Support is fast and helpful. Every issue was resolved quickly. Great experience!", "author": {"name": "<PERSON>", "title": "Product Manager", "company": "CloudSaaS", "image": "/testimonials/3.jpg"}}]}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Everything you need to know about Ship SaaS Demo", "faqs": [{"question": "How do I get started with Ship SaaS Demo?", "answer": "Just sign up, pick a plan, and deploy your SaaS project in one click."}, {"question": "What payment methods are supported?", "answer": "Built-in Stripe subscription payments, supporting major credit cards and multi-currency."}, {"question": "Does the template support i18n and SEO?", "answer": "Yes, multi-language switching and SEO best practices are built-in for global reach."}, {"question": "Can I customize features and UI?", "answer": "Absolutely. The template is fully open source for easy customization and branding."}, {"question": "How do I get support if I run into issues?", "answer": "We offer 24/7 technical support. Our team is always ready to help."}]}, "cta": {"title": "Ready to launch your SaaS at lightning speed?", "subtitle": "Join developers worldwide and experience the efficiency of a one-stop SaaS template", "cta": {"primary": "Try the Demo Now", "secondary": "View Pricing"}}, "auth": {"signInTitle": "Sign in to your account", "signInDescription": "Access your account to continue", "signInWithGoogle": "Sign in with Google", "signInWithGithub": "Sign in with GitHub", "signingIn": "Signing in...", "oauthError": "An error occurred during sign in. Please try again.", "authError": "Authentication error. Please try again.", "signIn": "Sign In", "email": "Email address", "password": "Password", "confirmPassword": "Confirm Password", "noAccount": "Don't have an account? Sign up", "alreadyHaveAccount": "Already have an account? Sign in", "orContinueWith": "Or continue with", "invalidCredentials": "Invalid email or password", "signInError": "An error occurred during sign in. Please try again.", "signUp": "Sign Up", "signingUp": "Signing up...", "createAccount": "Create a new account", "signUpError": "An error occurred during sign up. Please try again.", "passwordsDoNotMatch": "Passwords do not match", "signUpSuccess": "Account created successfully! Signing you in..."}, "footer": {"newsletter": {"title": "Stay Connected", "description": "Subscribe to our newsletter for the latest AI image generation tips, updates, and exclusive offers.", "placeholder": "Enter your email", "subscribe": "Subscribe", "subscribeAria": "Subscribe to newsletter"}, "quickLinks": {"title": "Quick Links", "home": "Home", "features": "Features", "pricing": "Pricing", "blog": "Blog", "about": "About Us"}, "resources": {"title": "Resources", "documentation": "Documentation", "tutorials": "Tutorials", "community": "Community", "support": "Support Center", "api": "API Reference"}, "company": {"title": "Company", "about": "About FluxAI", "careers": "Careers", "press": "Press Kit", "contact": "Contact Us", "partners": "Partners"}, "social": {"title": "Follow Us", "facebook": "Follow us on Facebook", "twitter": "Follow us on Twitter", "instagram": "Follow us on Instagram", "linkedin": "Connect with us on LinkedIn", "youtube": "Subscribe to our YouTube", "discord": "Join our Discord"}, "legal": {"title": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>", "dmca": "DMCA Policy"}, "contact": {"title": "Contact Info", "address": "San Francisco, California", "email": "<EMAIL>", "phone": "+****************", "hours": "24/7 Support Available"}, "theme": {"light": "Light Mode", "dark": "Dark Mode", "toggle": "Toggle Theme"}, "copyright": "© 2024 FluxAI Studio. All rights reserved.", "tagline": "Empowering creativity with AI"}, "orders": {"title": "My Orders", "description": "View all your order history and status", "noOrders": "No orders found", "orderDetails": {"purchase": "Purchase", "orderId": "Order ID", "amount": "Amount", "orderDate": "Order Date", "paidDate": "Paid <PERSON>", "status": {"paid": "Paid", "pending": "Pending", "failed": "Failed", "expired": "Expired"}}}, "profile": {"title": "Profile", "subtitle": "View and manage your personal information", "personalInfo": "Personal Information", "uuid": "Unique ID", "email": "Email", "nickname": "Nickname", "registrationDate": "Registration Date", "signinMethod": "Sign-in Method", "lastSigninIp": "Last Sign-in IP", "unknown": "Unknown"}}